<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 18:13:21
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button preIcon="icon-ym icon-ym-btn-download" type="primary" @click="handleExport">导出</a-button>
          </template>
        </BasicTable>
      </div>
    </div>
    <ExportModal @register="registerExportModal" @download="handleDownload" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useModal } from '@/components/Modal';
  import { BasicTable, useTable, BasicColumn, TableAction, ActionItem } from '@/components/Table';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/knsPdxx');
  const { createMessage } = useMessage();

  const columns: BasicColumn[] = [
    { title: '学号', dataIndex: 'studentNo', width: 100, resizable: true, ellipsis: true, fixed: 'left' },
    { title: '姓名', dataIndex: 'name', width: 100, resizable: true, ellipsis: true, fixed: 'left' },
    { title: '院系', dataIndex: 'college', width: 180, resizable: true, ellipsis: true },
    { title: '专业', dataIndex: 'major', width: 140, resizable: true, ellipsis: true },
    { title: '申请时间', dataIndex: 'applyDate', width: 150, resizable: true, ellipsis: true, format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '申请描述', dataIndex: 'applyReason', width: 160, resizable: true, ellipsis: true },
    { title: '评定学年', dataIndex: 'year', width: 120, resizable: true, ellipsis: true },
    { title: '评定学期', dataIndex: 'term', width: 100, resizable: true, ellipsis: true },
    { title: '申请困难等级', dataIndex: 'applyType', width: 120, resizable: true, ellipsis: true },
    { title: '评定困难等级', dataIndex: 'difficultType', width: 120, resizable: true, ellipsis: true },
    { title: '性别', dataIndex: 'gender', width: 60, resizable: true, ellipsis: true },
    { title: '出生日期', dataIndex: 'birthday', width: 120, resizable: true, ellipsis: true },
    { title: '血型', dataIndex: 'bloodType', width: 80, resizable: true, ellipsis: true },
    { title: '民族', dataIndex: 'nation', width: 80, resizable: true, ellipsis: true },
    { title: '身份证件类型', dataIndex: 'idType', width: 120, resizable: true, ellipsis: true },
    { title: '身份证号', dataIndex: 'idNo', width: 180, resizable: true, ellipsis: true },
  ];

  const formConfig = {
    schemas: [
      {
        field: 'keyword',
        label: '关键字',
        component: 'Input',
        componentProps: {
          placeholder: '请输入学号/姓名',
        },
      },
      {
        field: 'year',
        label: '学年',
        component: 'Select',
        componentProps: {
          placeholder: '请选择学年',
          options: [
            { fullName: '2023-2024学年', value: '2023-2024学年' },
            { fullName: '2022-2023学年', value: '2022-2023学年' },
            { fullName: '2021-2022学年', value: '2021-2022学年' },
            { fullName: '2020-2021学年', value: '2020-2021学年' },
            { fullName: '2019-2020学年', value: '2019-2020学年' },
          ],
        },
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: params => api.getList({ params }),
    columns,
    useSearchForm: true,
    formConfig,
    showIndexColumn: true,
    rowKey: 'id',
  });

  const [registerExportModal, { openModal: openExportModal }] = useModal();

  function handleExport() {
    openExportModal(true, { columnList: columns });
  }
  function handleDownload(data) {
    createMessage.success('导出成功');
  }
</script>
