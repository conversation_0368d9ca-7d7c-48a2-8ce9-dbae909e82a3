<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- 按钮组区 -->
            <a-button type="primary" @click="handleExport">导出当前表格</a-button>
            <!-- <a-button @click="handleExport">导出答题明细</a-button> -->
          </template>
          <template #bodyCell="{ column, record }">
            <!-- 表格列插槽区 -->
            <template v-if="column.key === 'xjztmc'">
              <a-tag color="success">
                {{ record.xjztmc }}
              </a-tag>
            </template>

            <template v-if="column.key === 'df'">
              <span :style="{ color: record.df ? '#1890ff' : '#999' }">
                {{ record.df || '-' }}
              </span>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 导出 -->
    <ExportModal @register="registerExportModal" />
  </div>
</template>

<script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import { onMounted, ref } from 'vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useBaseStore } from '@/store/modules/base';
  import * as schoolApi from '@/api/school';

  const props = defineProps(['wjdm']);
  const api = useBaseApi('/api/knsDcwjPerson');

  // #region table
  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '学号',
      dataIndex: 'xsbh',
      width: 120,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'xm',
      width: 100,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '学籍状态',
      dataIndex: 'xjztmc',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '性别',
      dataIndex: 'xbmc',
      width: 90,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '出生日期',
      dataIndex: 'csrq',
      width: 150,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '学院',
      dataIndex: 'dwmc',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '专业',
      dataIndex: 'zymc',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '现在年级',
      dataIndex: 'xznj',
      width: 100,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '班级',
      dataIndex: 'bjmc',
      resizable: true,
      ellipsis: true,
    },

    {
      title: '学生类别',
      dataIndex: 'xslbmc',
      width: 120,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '参与情况',
      dataIndex: 'cjqk',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学年',
      dataIndex: 'txxn',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写学期',
      dataIndex: 'txxq',
      width: 100,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '填写时间',
      dataIndex: 'txsj',
      width: 150,
      align: 'center',
      resizable: true,
      ellipsis: true,
    },
    {
      title: '得分',
      dataIndex: 'df',
      width: 100,
      align: 'center',
      fixed: 'right',
      resizable: true,
      ellipsis: true,
    },
  ];

  // 注册表格
  const [registerTable, { reload, getForm, getFetchParams, setColumns, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: params => api.request('get', '/getList', { params: { ...params, wjdm: props.wjdm } }),
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: '关键词',
          component: 'Input',
          componentProps: {
            placeholder: '请输入学号/姓名关键词',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '学院',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            clearImmediate: false,

            onChange: (val, obj) => {
              loadZY(val);
            },
          },
        },
        {
          field: 'zydm',
          label: '专业',
          component: 'Select',
          componentProps: {
            placeholder: '全部',

            onChange: (val, obj) => {
              loadNJ(val);
            },
          },
        },
        {
          field: 'nj',
          label: '现在年级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            onChange: (val, obj) => {
              loadBJ(val);
            },
          },
        },
        {
          field: 'bjdm',
          label: '班级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
          },
        },
      ],
    },
    immediate: false,
    showTableSetting: true,
    tableSetting: { fullScreen: true },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: () => {},
      },
    ];
  }

  // #endregion

  // #region 院系 年级  专业 班级
  function loadXY() {
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      const form = getForm();
      form.updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
      form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    });
  }
  async function loadZY(dwdm) {
    const form = getForm();
    form.updateSchema({ field: 'zydm', componentProps: { options: [] } });
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    schoolApi.getZY({ dwdm: dwdm, pageSize: 99999 }).then(res => {
      form.updateSchema({ field: 'zydm', componentProps: { options: res.data.list, fieldNames: { label: 'zyMc', value: 'zyDm' } } });
    });
  }
  function loadNJ(zydm) {
    const form = getForm();
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ nj: '', bjdm: '' });
    const state = form.getFieldsValue();

    schoolApi.getNJ({ dwdm: state.dwdm, zydm: zydm, pageSize: 99999 }).then(res => {
      const data = res.data.list.map(item => {
        return { label: item, value: item };
      });
      form.updateSchema({ field: 'nj', componentProps: { options: data, fieldNames: { label: 'label', value: 'value' } } });
    });
  }

  function loadBJ(nj) {
    const state = getForm().getFieldsValue();
    schoolApi.getBJ({ dwdm: state.dwdm, zydm: state.zydm, nj: nj }).then(res => {
      getForm().updateSchema({ field: 'bjdm', componentProps: { options: res.data.list, fieldNames: { label: 'bjMc', value: 'bjDm' } } });
    });
  }
  //#endregion

  // #region 导出
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  async function handleExport() {
    // 获取查询参数
    const listQuery = getFetchParams();
    openExportModal(true, { listQuery, exportType: 'BusinessKnsDcwjPersonVo', apiUrl: '/api/knsDcwjPerson/exportStatistics' });
  }

  // #endregion

  onMounted(() => {
    loadXY();

    reload();
  });
</script>
