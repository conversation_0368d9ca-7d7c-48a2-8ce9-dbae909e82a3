<!--
 * @Description: 问卷统计详情
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 16:21:26
-->
<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="统计详情" cancelText="关闭" :showOkBtn="false" :bodyStyle="{ height: '600px' }">
    <a-tabs v-model:activeKey="activeKey" :destroyInactiveTabPane="true">
      <a-tab-pane key="1">
        <template #tab>
          <div class="pl-10px pr-10px">学院统计</div>
        </template>
        <CollageChart v-if="activeKey === '1'" :wjdm="state.wjdm" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="作答明细">
        <AnswerDetail v-if="activeKey === '2'" :wjdm="state.wjdm" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="调查明细">
        <StaticTable v-if="activeKey === '3'" :wjdm="state.wjdm" />
      </a-tab-pane>
    </a-tabs>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import { Spin, Empty } from 'ant-design-vue';
  import CollageChart from './CollageChart.vue';
  import AnswerDetail from './AnswerDetail.vue';
  import StaticTable from './StaticTable.vue';
  const activeKey = ref('1');
  const state = ref({});
  const [registerPopup, { changeLoading, changeOkLoading, closePopup }] = usePopupInner(init);
  function init(data) {
    state.value = data;
  }
</script>
