/*
 * @Description: 困难学生设置相关API
 * @Autor: panmy
 * @Date: 2025-08-01 10:00:00
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 10:00:00
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/knsSz',
}

// 获取一个设置
export function getKnsSzOne() {
  return defHttp.get({ url: Api.Prefix + '/getOne' });
}

// 保存设置
export function saveKnsSz(data) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

// 更新设置
export function updateKnsSz(data) {
  return defHttp.put({ url: Api.Prefix + '/update', data });
}
